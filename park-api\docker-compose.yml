version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0.35
    container_name: park-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 5Yy8XJj6nzBq
      MYSQL_DATABASE: parknew
      MYSQL_USER: admin
      MYSQL_PASSWORD: K7PmdL9Rf2Q
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_logs:/var/log/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p5Yy8XJj6nzBq"]
      timeout: 20s
      retries: 10
      interval: 10s
    networks:
      - park-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: park-redis
    restart: always
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    command: redis-server --appendonly yes --requirepass qR6bW9kFzT3Zv
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "qR6bW9kFzT3Zv", "ping"]
      timeout: 10s
      retries: 5
      interval: 10s
    networks:
      - park-network

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: park-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: 5Yy8XJj6nzBq
      NACOS_AUTH_ENABLE: "true"
      NACOS_AUTH_TOKEN: "pSx0LtJm3YKb6L0yM7QNV1R5C1yCX72aIVL7eFjtRhJvqRiP5dununS3jAsNZIdz"
      NACOS_AUTH_IDENTITY_KEY: "serverIdentity"
      NACOS_AUTH_IDENTITY_VALUE: "security"
      # 初始化默认用户
      # NACOS_AUTH_SYSTEM_TYPE: "nacos"
      # NACOS_AUTH_CACHE_ENABLE: "false"
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_logs:/home/<USER>/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      timeout: 10s
      retries: 10
      interval: 15s
    networks:
      - park-network

  # 认证服务
  park-auth:
    image: park/lgjy-auth:3.6.9
    container_name: park-auth
    restart: always
    ports:
      - "9204:9204"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 注意：数据库和Redis配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - park_auth_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9204/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 微信认证服务
  park-wx-auth:
    image: park/lgjy-wx-auth:3.6.9
    container_name: park-wx-auth
    restart: always
    ports:
      - "9205:9205"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 注意：Redis、短信服务和微信API配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - park_wx_auth_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9205/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 系统管理服务
  park-system:
    image: park/lgjy-system:3.6.9
    container_name: park-system
    restart: always
    ports:
      - "9201:9201"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 注意：数据库、Redis和MyBatis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_system_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9201/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 文件服务
  park-file:
    image: park/lgjy-file:3.6.9
    container_name: park-file
    restart: always
    ports:
      - "9202:9202"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 注意：文件存储配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - /home/<USER>/images:/app/upload  # ✅ 直接映射
      - park_file_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9202/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 微信小程序服务
  park-wx:
    image: park/lgjy-wx:3.6.9
    container_name: park-wx
    restart: always
    ports:
      - "9206:9206"  # 修复端口冲突：从9202改为9206
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 注意：数据库、Redis、微信API和银联支付配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_wx_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9206/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 道闸设备服务
  park-gate:
    image: park/lgjy-gate:3.6.9
    container_name: park-gate
    restart: always
    ports:
      - "9203:9203"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 数据库和Redis配置通过环境变量传递给Nacos配置
      DB_HOST: park-mysql
      DB_PORT: 3306
      DB_NAME: parknew
      DB_USERNAME: admin
      DB_PASSWORD: K7PmdL9Rf2Q
      REDIS_HOST: park-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: qR6bW9kFzT3Zv
      REDIS_DATABASE: 2
      # 注意：数据库和Redis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_gate_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9203/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 网关服务
  park-gateway:
    image: park/lgjy-gateway:3.6.9
    container_name: park-gateway
    restart: always
    ports:
      - "8080:8080"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: db222c85-606a-489b-95ac-ac568029b9b2
      # 注意：Redis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_gateway_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network


volumes:
  mysql_data:
  mysql_logs:
  redis_data:
  redis_logs:
  nacos_logs:
  file_data:
  park_auth_logs:
  park_wx_auth_logs:
  park_system_logs:
  park_file_logs:
  park_wx_logs:
  park_gate_logs:
  park_gateway_logs:

networks:
  park-network:
    driver: bridge